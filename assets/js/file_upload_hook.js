// File Upload Hook for Phoenix LiveView
// Handles file selection and uploads raw file content to server for storage

const FileUpload = {
  mounted() {
    this.el.addEventListener('change', (event) => {
      const file = event.target.files[0];

      if (!file) {
        return;
      }

      // Check file size (16MB limit)
      const maxSize = 16 * 1024 * 1024; // 16MB
      if (file.size > maxSize) {
        alert(`File too large. Maximum size is 16MB, but file is ${this.formatFileSize(file.size)}.`);
        this.el.value = ''; // Clear the file input
        return;
      }

      // Read file as raw binary data
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          // Get raw file content as ArrayBuffer
          const arrayBuffer = e.target.result;
          const uint8Array = new Uint8Array(arrayBuffer);

          // Convert to base64 for transmission (will be stored as raw on server)
          const base64Content = btoa(String.fromCharCode.apply(null, uint8Array));

          // Send file upload event to LiveView
          this.pushEvent("file_uploaded", {
            filename: file.name,
            content: base64Content,
            size: file.size,
            type: file.type
          });

          // Show file info
          console.log(`File uploaded: ${file.name} (${this.formatFileSize(file.size)})`);

          // Update file info display if it exists
          const fileInfoElement = document.querySelector('.file-info-display');
          if (fileInfoElement) {
            fileInfoElement.innerHTML = `
              <div class="text-sm text-base-content/70">
                <p><strong>File:</strong> ${file.name}</p>
                <p><strong>Size:</strong> ${this.formatFileSize(file.size)}</p>
                <p><strong>Type:</strong> ${file.type}</p>
              </div>
            `;
            fileInfoElement.style.display = 'block';
          }

        } catch (error) {
          console.error('Error processing file:', error);
          alert('Failed to process file. Please try again.');
          this.el.value = ''; // Clear the file input
        }
      };

      reader.onerror = () => {
        alert('Failed to read file. Please try again.');
        this.el.value = ''; // Clear the file input
      };

      // Read file as ArrayBuffer (raw binary)
      reader.readAsArrayBuffer(file);
    });
  },

  // Helper function to format file size
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
};

export default FileUpload;
